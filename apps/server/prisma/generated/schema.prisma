generator client {
  provider = "prisma-client-js"
  output   = "../generated"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// Enable pgvector extension for embeddings
generator pgvector {
  provider        = "prisma-client-js"
  previewFeatures = ["postgresqlExtensions"]
}

model Workspace {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  plan      Plan     @default(FREE)
  settings  Json?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  users    User[]
  projects Project[]
  reports  Report[]

  @@map("workspaces")
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  fullName    String?  @map("full_name")
  avatarUrl   String?  @map("avatar_url")
  workspaceId String?  @map("workspace_id")
  role        UserRole @default(MEMBER)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  workspace Workspace? @relation(fields: [workspaceId], references: [id])
  projects  Project[]
  reports   Report[]

  @@map("users")
}

model Project {
  id                String      @id @default(cuid())
  name              String
  type              ProjectType
  identifier        String // domain, twitter handle, or contract address
  description       String?
  website           String?
  twitterHandle     String?     @map("twitter_handle")
  githubUrl         String?     @map("github_url")
  contractAddresses Json?       @map("contract_addresses")
  metadata          Json?
  createdBy         String      @map("created_by")
  workspaceId       String      @map("workspace_id")
  createdAt         DateTime    @default(now()) @map("created_at")
  updatedAt         DateTime    @updatedAt @map("updated_at")

  // Relations
  creator     User         @relation(fields: [createdBy], references: [id])
  workspace   Workspace    @relation(fields: [workspaceId], references: [id])
  reports     Report[]
  dataSources DataSource[]
  embeddings  Embedding[]

  @@unique([identifier, workspaceId])
  @@map("projects")
}

model Report {
  id                   String       @id @default(cuid())
  projectId            String       @map("project_id")
  title                String
  status               ReportStatus @default(PENDING)
  depth                ReportDepth  @default(STANDARD)
  content              Json?
  executiveSummary     String?      @map("executive_summary")
  marketAnalysis       Json?        @map("market_analysis")
  competitiveLandscape Json?        @map("competitive_landscape")
  technicalAnalysis    Json?        @map("technical_analysis")
  tokenomicsAnalysis   Json?        @map("tokenomics_analysis")
  growthMetrics        Json?        @map("growth_metrics")
  recommendations      Json?
  sources              Json?
  generatedBy          String       @map("generated_by")
  workspaceId          String       @map("workspace_id")
  createdAt            DateTime     @default(now()) @map("created_at")
  updatedAt            DateTime     @updatedAt @map("updated_at")
  completedAt          DateTime?    @map("completed_at")

  // Relations
  project   Project    @relation(fields: [projectId], references: [id])
  generator User       @relation(fields: [generatedBy], references: [id])
  workspace Workspace  @relation(fields: [workspaceId], references: [id])
  agentRuns AgentRun[]

  @@map("reports")
}

model AgentRun {
  id           String         @id @default(cuid())
  reportId     String         @map("report_id")
  agentType    String         @map("agent_type")
  status       AgentRunStatus @default(PENDING)
  inputData    Json?          @map("input_data")
  outputData   Json?          @map("output_data")
  errorMessage String?        @map("error_message")
  metrics      Json?
  startedAt    DateTime?      @map("started_at")
  completedAt  DateTime?      @map("completed_at")
  createdAt    DateTime       @default(now()) @map("created_at")
  updatedAt    DateTime       @updatedAt @map("updated_at")

  // Relations
  report Report @relation(fields: [reportId], references: [id])

  @@map("agent_runs")
}

model DataSource {
  id              String    @id @default(cuid())
  projectId       String    @map("project_id")
  sourceType      String    @map("source_type")
  sourceUrl       String    @map("source_url")
  data            Json
  hash            String    @unique
  confidenceScore Float?    @map("confidence_score")
  lastUpdated     DateTime  @default(now()) @map("last_updated")
  expiresAt       DateTime? @map("expires_at")
  createdAt       DateTime  @default(now()) @map("created_at")

  // Relations
  project    Project     @relation(fields: [projectId], references: [id])
  embeddings Embedding[]

  @@map("data_sources")
}

model Embedding {
  id        String   @id @default(cuid())
  content   String
  embedding String // Vector stored as string
  metadata  Json?
  projectId String?  @map("project_id")
  sourceId  String?  @map("source_id")
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  project Project?    @relation(fields: [projectId], references: [id])
  source  DataSource? @relation(fields: [sourceId], references: [id])

  @@map("embeddings")
}

// Enums
enum Plan {
  FREE
  PRO
  ENTERPRISE

  @@map("plan")
}

enum UserRole {
  ADMIN
  MEMBER
  VIEWER

  @@map("user_role")
}

enum ProjectType {
  DOMAIN
  TWITTER
  CONTRACT

  @@map("project_type")
}

enum ReportStatus {
  PENDING
  GENERATING
  COMPLETED
  FAILED

  @@map("report_status")
}

enum ReportDepth {
  QUICK
  STANDARD
  DEEP

  @@map("report_depth")
}

enum AgentRunStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED

  @@map("agent_run_status")
}
