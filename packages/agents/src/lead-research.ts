import { BaseAgent } from './base';
import type { AgentContext, AgentResult } from './base';
import { SYSTEM_PROMPTS } from '@cma/ai';
import { z } from 'zod';

const ResearchPlanSchema = z.object({
  project: z.object({
    name: z.string(),
    type: z.enum(['defi', 'nft', 'gaming', 'infrastructure', 'dao', 'other']),
    description: z.string(),
    website: z.string().optional(),
    twitter: z.string().optional(),
    github: z.string().optional(),
    contracts: z.array(z.string()).optional(),
  }),
  researchScope: z.object({
    onchainAnalytics: z.object({
      priority: z.enum(['high', 'medium', 'low']),
      focus: z.array(z.string()),
      chains: z.array(z.string()),
    }),
    socialSentiment: z.object({
      priority: z.enum(['high', 'medium', 'low']),
      platforms: z.array(z.string()),
      metrics: z.array(z.string()),
    }),
    competitorAnalysis: z.object({
      priority: z.enum(['high', 'medium', 'low']),
      directCompetitors: z.array(z.string()),
      indirectCompetitors: z.array(z.string()),
    }),
    technicalAssessment: z.object({
      priority: z.enum(['high', 'medium', 'low']),
      focus: z.array(z.string()),
    }),
    tokenomicsAnalysis: z.object({
      priority: z.enum(['high', 'medium', 'low']),
      aspects: z.array(z.string()),
    }),
    marketPositioning: z.object({
      priority: z.enum(['high', 'medium', 'low']),
      sectors: z.array(z.string()),
    }),
  }),
  timeline: z.object({
    estimatedDuration: z.number(),
    agentSequence: z.array(z.string()),
  }),
});

export class LeadResearchAgent extends BaseAgent {
  constructor() {
    super('lead_research', SYSTEM_PROMPTS.LEAD_RESEARCHER);
  }

  async execute(context: AgentContext, input: any): Promise<AgentResult> {
    try {
      await this.updateProgress(context.reportId, {
        step: 'analyzing_input',
        progress: 10,
        status: 'running',
        message: 'Analyzing project input and identifying research scope',
      });

      // Step 1: Analyze the input and identify the project
      const projectAnalysis = await this.analyzeProjectInput(input);

      await this.updateProgress(context.reportId, {
        step: 'creating_research_plan',
        progress: 30,
        status: 'running',
        message: 'Creating comprehensive research plan',
      });

      // Step 2: Create a comprehensive research plan
      const researchPlan = await this.createResearchPlan(projectAnalysis, context.reportDepth);

      await this.updateProgress(context.reportId, {
        step: 'validating_plan',
        progress: 50,
        status: 'running',
        message: 'Validating research plan and resource requirements',
      });

      // Step 3: Validate and optimize the research plan
      const optimizedPlan = await this.optimizeResearchPlan(researchPlan);

      await this.updateProgress(context.reportId, {
        step: 'gathering_initial_data',
        progress: 70,
        status: 'running',
        message: 'Gathering initial project data and context',
      });

      // Step 4: Gather initial project context
      const initialContext = await this.gatherInitialContext(optimizedPlan.project);

      await this.updateProgress(context.reportId, {
        step: 'finalizing_plan',
        progress: 90,
        status: 'running',
        message: 'Finalizing research plan with enriched context',
      });

      // Step 5: Enrich the plan with initial context
      const finalPlan = {
        ...optimizedPlan,
        initialContext,
        context,
      };

      await this.updateProgress(context.reportId, {
        step: 'completed',
        progress: 100,
        status: 'completed',
        message: 'Research plan created successfully',
      });

      const result: AgentResult = {
        success: true,
        data: finalPlan,
        sources: [`Input analysis: ${input.type}:${input.value}`],
        metrics: {
          analysisTime: Date.now(),
          projectType: projectAnalysis.type,
          researchComplexity: this.calculateComplexity(optimizedPlan),
        },
      };

      await this.logAgentRun(context.reportId, input, result);
      return result;

    } catch (error) {
      const result: AgentResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error in lead research',
      };

      await this.logAgentRun(context.reportId, input, result);
      return result;
    }
  }

  private async analyzeProjectInput(input: any): Promise<any> {
    const prompt = `
Analyze the following project input and provide comprehensive project identification:

Input Type: ${input.type}
Input Value: ${input.value}

Based on this input, identify:
1. Project name and description
2. Project category (DeFi, NFT, Gaming, Infrastructure, DAO, Other)
3. All available resources (website, social media, repositories)
4. Contract addresses if applicable
5. Key characteristics that will guide research priorities

Provide specific, actionable analysis that will inform the research strategy.
`;

    return await this.generateStructuredResponse(
      prompt,
      z.object({
        name: z.string(),
        type: z.enum(['defi', 'nft', 'gaming', 'infrastructure', 'dao', 'other']),
        description: z.string(),
        website: z.string().optional(),
        twitter: z.string().optional(),
        github: z.string().optional(),
        contracts: z.array(z.string()).optional(),
        confidence: z.number().min(0).max(1),
        additionalResources: z.array(z.string()).optional(),
      }),
      input
    );
  }

  private async createResearchPlan(projectAnalysis: any, reportDepth: string): Promise<any> {
    const prompt = `
Create a comprehensive research plan for the following project:

Project: ${JSON.stringify(projectAnalysis, null, 2)}
Report Depth: ${reportDepth}

Design a research strategy that:
1. Prioritizes agents based on project type and available data
2. Defines specific research focus areas for each agent
3. Identifies data sources and collection methods
4. Estimates timeline and resource requirements
5. Sequences agent execution for optimal results

Consider the report depth when determining scope and detail level:
- Quick: Focus on key metrics and high-level analysis
- Standard: Comprehensive analysis across all areas
- Deep: Detailed technical and strategic analysis
`;

    return await this.generateStructuredResponse(prompt, ResearchPlanSchema, {
      projectAnalysis,
      reportDepth,
    });
  }

  private async optimizeResearchPlan(plan: any): Promise<any> {
    // Optimize the research plan based on available resources and constraints
    const optimizations = {
      // Adjust priorities based on data availability
      priorityAdjustments: this.adjustPriorities(plan),
      // Optimize agent execution sequence
      sequenceOptimization: this.optimizeSequence(plan),
      // Resource allocation
      resourceAllocation: this.allocateResources(plan),
    };

    return {
      ...plan,
      optimizations,
      estimatedDuration: this.calculateDuration(plan),
    };
  }

  private async gatherInitialContext(project: any): Promise<any> {
    // Gather basic project information to provide context for other agents
    const context = {
      projectMetadata: project,
      discoveredResources: [] as any[],
      initialMetrics: {},
      researchHints: [] as any[],
    };

    // Add any immediately available information
    if (project.website) {
      context.discoveredResources.push({
        type: 'website',
        url: project.website,
        priority: 'high',
      });
    }

    if (project.twitter) {
      context.discoveredResources.push({
        type: 'twitter',
        handle: project.twitter,
        priority: 'high',
      });
    }

    return context;
  }

  private adjustPriorities(plan: any): any {
    // Adjust agent priorities based on project type and available data
    const adjustments: any = {};

    // DeFi projects need strong on-chain and tokenomics analysis
    if (plan.project.type === 'defi') {
      adjustments.onchainAnalytics = 'high';
      adjustments.tokenomicsAnalysis = 'high';
    }

    // NFT projects need strong social sentiment and market positioning
    if (plan.project.type === 'nft') {
      adjustments.socialSentiment = 'high';
      adjustments.marketPositioning = 'high';
    }

    // Infrastructure projects need technical assessment
    if (plan.project.type === 'infrastructure') {
      adjustments.technicalAssessment = 'high';
    }

    return adjustments;
  }

  private optimizeSequence(plan: any): string[] {
    // Optimize the sequence of agent execution for best results
    const sequence = [];

    // Always start with on-chain analytics for hard data
    sequence.push('onchain_analytics');

    // Social sentiment can run in parallel
    sequence.push('social_sentiment');

    // Competitor analysis benefits from on-chain data
    sequence.push('competitor_analysis');

    // Technical assessment can run independently
    sequence.push('technical_assessment');

    // Tokenomics needs on-chain data
    sequence.push('tokenomics_analysis');

    // Market positioning synthesizes other analyses
    sequence.push('market_positioning');

    return sequence;
  }

  private allocateResources(plan: any): any {
    return {
      apiCalls: {
        budget: 1000,
        distribution: {
          onchain: 300,
          social: 200,
          competitor: 150,
          technical: 100,
          tokenomics: 150,
          market: 100,
        },
      },
      timeAllocation: {
        total: 300, // 5 minutes
        perAgent: 50,
      },
    };
  }

  private calculateDuration(plan: any): number {
    // Calculate estimated duration based on plan complexity
    const baseTime = 180; // 3 minutes base
    const complexityMultiplier = this.calculateComplexity(plan);
    return Math.round(baseTime * complexityMultiplier);
  }

  private calculateComplexity(plan: any): number {
    // Calculate research complexity score
    let complexity = 1.0;

    // Add complexity based on number of high-priority agents
    const highPriorityAgents = Object.values(plan.researchScope)
      .filter((scope: any) => scope.priority === 'high').length;
    complexity += highPriorityAgents * 0.2;

    // Add complexity based on project type
    const typeComplexity = {
      defi: 1.3,
      infrastructure: 1.2,
      dao: 1.1,
      nft: 1.0,
      gaming: 1.1,
      other: 1.0,
    };
    complexity *= typeComplexity[plan.project.type as keyof typeof typeComplexity] || 1.0;

    return Math.min(complexity, 2.0); // Cap at 2x
  }
}