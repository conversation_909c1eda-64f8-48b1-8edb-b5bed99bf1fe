import { BaseAgent } from './base';
import type { AgentContext, AgentResult } from './base';
import { LeadResearchAgent } from './lead-research';
import { OnChainAnalyticsAgent } from './onchain-analytics';
import { SocialSentimentAgent } from './social-sentiment';
import { CompetitorAnalysisAgent } from './competitor-analysis';
import { TechnicalAssessmentAgent } from './technical-assessment';
import { TokenomicsAnalysisAgent } from './tokenomics-analysis';
import { MarketPositioningAgent } from './market-positioning';
import { createServerClient } from '@cma/database';

export interface OrchestrationResult {
  success: boolean;
  results: Record<string, AgentResult>;
  reportData: any;
  error?: string;
}

export class AgentOrchestrator {
  private db = createServerClient();
  private agents: Map<string, BaseAgent> = new Map();

  constructor() {
    this.initializeAgents();
  }

  private initializeAgents(): void {
    this.agents.set('lead_research', new LeadResearchAgent());
    this.agents.set('onchain_analytics', new OnChainAnalyticsAgent());
    this.agents.set('social_sentiment', new SocialSentimentAgent());
    this.agents.set('competitor_analysis', new CompetitorAnalysisAgent());
    this.agents.set('technical_assessment', new TechnicalAssessmentAgent());
    this.agents.set('tokenomics_analysis', new TokenomicsAnalysisAgent());
    this.agents.set('market_positioning', new MarketPositioningAgent());
  }

  async orchestrateResearch(context: AgentContext, projectInput: any): Promise<OrchestrationResult> {
    try {
      await this.updateReportStatus(context.reportId, 'generating');

      // Step 1: Lead Research Agent analyzes input and creates research plan
      const leadAgent = this.agents.get('lead_research')!;
      const leadResult = await leadAgent.execute(context, projectInput);
      
      if (!leadResult.success) {
        await this.updateReportStatus(context.reportId, 'failed');
        return {
          success: false,
          results: { lead_research: leadResult },
          reportData: null,
          error: 'Lead research failed: ' + leadResult.error,
        };
      }

      const researchPlan = leadResult.data;
      
      // Step 2: Execute specialized agents in parallel based on research plan
      const agentTasks = this.createAgentTasks(context, researchPlan);
      const agentResults = await this.executeAgentsInParallel(agentTasks);

      // Step 3: Synthesize results into comprehensive report
      const reportData = await this.synthesizeResults(context, agentResults, researchPlan);

      // Step 4: Update report with final data
      await this.updateReportWithResults(context.reportId, reportData);
      await this.updateReportStatus(context.reportId, 'completed');

      return {
        success: true,
        results: agentResults,
        reportData,
      };

    } catch (error) {
      await this.updateReportStatus(context.reportId, 'failed');
      console.error('Orchestration failed:', error);
      
      return {
        success: false,
        results: {},
        reportData: null,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private createAgentTasks(context: AgentContext, researchPlan: any): Array<{
    agentType: string;
    agent: BaseAgent;
    input: any;
  }> {
    const tasks = [];

    // Determine which agents to run based on research plan and report depth
    const agentConfigs = [
      { type: 'onchain_analytics', required: true },
      { type: 'social_sentiment', required: true },
      { type: 'competitor_analysis', required: context.reportDepth !== 'quick' },
      { type: 'technical_assessment', required: context.reportDepth === 'deep' },
      { type: 'tokenomics_analysis', required: context.reportDepth !== 'quick' },
      { type: 'market_positioning', required: true },
    ];

    for (const config of agentConfigs) {
      if (config.required) {
        const agent = this.agents.get(config.type);
        if (agent) {
          tasks.push({
            agentType: config.type,
            agent,
            input: {
              ...researchPlan,
              agentSpecificData: researchPlan[config.type] || {},
            },
          });
        }
      }
    }

    return tasks;
  }

  private async executeAgentsInParallel(tasks: Array<{
    agentType: string;
    agent: BaseAgent;
    input: any;
  }>): Promise<Record<string, AgentResult>> {
    const results: Record<string, AgentResult> = {};

    // Execute agents in parallel with error handling
    const promises = tasks.map(async (task) => {
      try {
        const result = await task.agent.execute(task.input.context, task.input);
        results[task.agentType] = result;
      } catch (error) {
        results[task.agentType] = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    });

    await Promise.allSettled(promises);
    return results;
  }

  private async synthesizeResults(
    context: AgentContext,
    agentResults: Record<string, AgentResult>,
    researchPlan: any
  ): Promise<any> {
    // Create comprehensive report structure
    const reportData = {
      executive_summary: this.generateExecutiveSummary(agentResults),
      market_analysis: this.extractMarketAnalysis(agentResults),
      competitive_landscape: this.extractCompetitiveLandscape(agentResults),
      technical_analysis: this.extractTechnicalAnalysis(agentResults),
      tokenomics_analysis: this.extractTokenomicsAnalysis(agentResults),
      growth_metrics: this.extractGrowthMetrics(agentResults),
      recommendations: this.generateRecommendations(agentResults),
      sources: this.aggregateSources(agentResults),
      metadata: {
        generated_at: new Date().toISOString(),
        depth: context.reportDepth,
        agents_used: Object.keys(agentResults),
        success_rate: this.calculateSuccessRate(agentResults),
      },
    };

    return reportData;
  }

  private generateExecutiveSummary(agentResults: Record<string, AgentResult>): string {
    // Combine key insights from all agents into executive summary
    const insights = [];
    
    for (const [agentType, result] of Object.entries(agentResults)) {
      if (result.success && result.data?.summary) {
        insights.push(`${agentType}: ${result.data.summary}`);
      }
    }

    return insights.join('\n\n');
  }

  private extractMarketAnalysis(agentResults: Record<string, AgentResult>): any {
    return {
      market_positioning: agentResults.market_positioning?.data || {},
      onchain_metrics: agentResults.onchain_analytics?.data?.market_metrics || {},
      competitive_context: agentResults.competitor_analysis?.data?.market_analysis || {},
    };
  }

  private extractCompetitiveLandscape(agentResults: Record<string, AgentResult>): any {
    return agentResults.competitor_analysis?.data?.competitive_landscape || {};
  }

  private extractTechnicalAnalysis(agentResults: Record<string, AgentResult>): any {
    return agentResults.technical_assessment?.data || {};
  }

  private extractTokenomicsAnalysis(agentResults: Record<string, AgentResult>): any {
    return agentResults.tokenomics_analysis?.data || {};
  }

  private extractGrowthMetrics(agentResults: Record<string, AgentResult>): any {
    return {
      onchain_growth: agentResults.onchain_analytics?.data?.growth_metrics || {},
      social_growth: agentResults.social_sentiment?.data?.growth_metrics || {},
    };
  }

  private generateRecommendations(agentResults: Record<string, AgentResult>): any {
    const recommendations = [];
    
    for (const [agentType, result] of Object.entries(agentResults)) {
      if (result.success && result.data?.recommendations) {
        recommendations.push({
          category: agentType,
          recommendations: result.data.recommendations,
        });
      }
    }

    return recommendations;
  }

  private aggregateSources(agentResults: Record<string, AgentResult>): string[] {
    const allSources = new Set<string>();
    
    for (const result of Object.values(agentResults)) {
      if (result.success && result.sources) {
        result.sources.forEach(source => allSources.add(source));
      }
    }

    return Array.from(allSources);
  }

  private calculateSuccessRate(agentResults: Record<string, AgentResult>): number {
    const total = Object.keys(agentResults).length;
    const successful = Object.values(agentResults).filter(r => r.success).length;
    return total > 0 ? (successful / total) * 100 : 0;
  }

  private async updateReportStatus(reportId: string, status: string): Promise<void> {
    try {
      const updateData: any = { 
        status, 
        updated_at: new Date().toISOString() 
      };
      
      if (status === 'completed') {
        updateData.completed_at = new Date().toISOString();
      }

      await this.db
        .from('reports')
        .update(updateData)
        .eq('id', reportId);
    } catch (error) {
      console.error('Failed to update report status:', error);
    }
  }

  private async updateReportWithResults(reportId: string, reportData: any): Promise<void> {
    try {
      await this.db
        .from('reports')
        .update({
          content: reportData,
          executive_summary: reportData.executive_summary,
          market_analysis: reportData.market_analysis,
          competitive_landscape: reportData.competitive_landscape,
          technical_analysis: reportData.technical_analysis,
          tokenomics_analysis: reportData.tokenomics_analysis,
          growth_metrics: reportData.growth_metrics,
          recommendations: reportData.recommendations,
          sources: reportData.sources,
          updated_at: new Date().toISOString(),
        })
        .eq('id', reportId);
    } catch (error) {
      console.error('Failed to update report with results:', error);
    }
  }
}