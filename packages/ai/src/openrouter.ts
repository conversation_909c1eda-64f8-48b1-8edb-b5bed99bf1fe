import { createOpenAI } from '@ai-sdk/openai';
import { generateObject, generateText } from 'ai';
import { z } from 'zod';

// OpenRouter client setup
const openrouter = createOpenAI({
  apiKey: process.env.OPENROUTER_API_KEY || '',
  baseURL: 'https://openrouter.ai/api/v1',
});

// Model configurations - Using models that support tool use
export const models = {
  // Use paid models that definitely support tool use
  geminiFlash: openrouter('google/gemini-1.5-flash'), // Paid version supports tools
  geminiPro: openrouter('google/gemini-1.5-pro'),
  claude35Sonnet: openrouter('anthropic/claude-3.5-sonnet'),
  gpt4o: openrouter('openai/gpt-4o'),
  gpt4oMini: openrouter('openai/gpt-4o-mini'),
  // Fallback free models for text generation only
  geminiFlashFree: openrouter('google/gemini-2.0-flash-exp:free'),
} as const;

// Model selection based on task complexity and requirements
export const getModel = (task: 'simple' | 'complex' | 'reasoning' = 'simple') => {
  console.log(`🤖 Selecting model for task: ${task}`);

  switch (task) {
    case 'simple':
      console.log(`📝 Using Gemini Flash for simple task`);
      return models.geminiFlash;
    case 'complex':
      console.log(`🧠 Using Gemini Pro for complex task`);
      return models.geminiPro;
    case 'reasoning':
      console.log(`🎯 Using Claude 3.5 Sonnet for reasoning task`);
      return models.claude35Sonnet;
    default:
      console.log(`📝 Using default Gemini Flash`);
      return models.geminiFlash;
  }
};

// Enhanced text generation with retry logic
export async function generateTextWithRetry(
  prompt: string,
  systemPrompt?: string,
  options: {
    model?: 'simple' | 'complex' | 'reasoning';
    maxTokens?: number;
    temperature?: number;
    retries?: number;
  } = {}
): Promise<{ text: string; usage?: any; error?: string }> {
  const {
    model = 'simple',
    maxTokens = 2000,
    temperature = 0.7,
    retries = 3,
  } = options;

  let lastError: Error | null = null;

  for (let attempt = 0; attempt < retries; attempt++) {
    try {
      console.log(`🤖 Generating text (attempt ${attempt + 1}/${retries})`);

      const result = await generateText({
        model: getModel(model),
        messages: [
          ...(systemPrompt ? [{ role: 'system' as const, content: systemPrompt }] : []),
          { role: 'user' as const, content: prompt },
        ],
        maxTokens,
        temperature,
      });

      console.log(`✅ Text generation successful`);
      return {
        text: result.text,
        usage: result.usage,
      };
    } catch (error) {
      lastError = error as Error;
      console.error(`❌ Text generation failed (attempt ${attempt + 1}):`, error);

      if (attempt < retries - 1) {
        // Wait before retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  return {
    text: '',
    error: lastError?.message || 'Text generation failed after all retries',
  };
}

// Structured object generation with validation
export async function generateStructuredData<T>(
  prompt: string,
  schema: z.ZodSchema<T>,
  systemPrompt?: string,
  options: {
    model?: 'simple' | 'complex' | 'reasoning';
    retries?: number;
  } = {}
): Promise<{ data?: T; error?: string }> {
  const { model = 'complex', retries = 3 } = options;

  let lastError: Error | null = null;

  for (let attempt = 0; attempt < retries; attempt++) {
    try {
      console.log(`🤖 Generating structured data (attempt ${attempt + 1}/${retries}) with model: ${model}`);

      const selectedModel = getModel(model);
      console.log(`📋 Using model for structured generation:`, selectedModel);

      const result = await generateObject({
        model: selectedModel,
        messages: [
          ...(systemPrompt ? [{ role: 'system' as const, content: systemPrompt }] : []),
          { role: 'user' as const, content: prompt },
        ],
        schema,
      });

      console.log(`✅ Structured data generation successful`);
      return { data: result.object };
    } catch (error) {
      lastError = error as Error;
      console.error(`❌ Structured data generation failed (attempt ${attempt + 1}):`, {
        error: error instanceof Error ? error.message : error,
        model,
        prompt: prompt.substring(0, 100) + '...',
      });

      // Check if it's a tool use error and try a different approach
      if (error instanceof Error && error.message.includes('tool use')) {
        console.log(`🔄 Tool use error detected, trying fallback approach...`);

        // Try using text generation with JSON parsing as fallback
        try {
          const textResult = await generateTextWithRetry(
            `${prompt}\n\nPlease respond with valid JSON that matches this schema structure. Be precise and follow the schema exactly.`,
            systemPrompt,
            { model: 'simple', retries: 1 }
          );

          if (textResult.text && !textResult.error) {
            // Try to parse the JSON response
            const jsonMatch = textResult.text.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              const parsedData = JSON.parse(jsonMatch[0]);
              const validatedData = schema.parse(parsedData);
              console.log(`✅ Fallback text-to-JSON generation successful`);
              return { data: validatedData };
            }
          }
        } catch (fallbackError) {
          console.error(`❌ Fallback approach also failed:`, fallbackError);
        }
      }

      if (attempt < retries - 1) {
        const delay = Math.pow(2, attempt) * 1000;
        console.log(`⏳ Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  return {
    error: lastError?.message || 'Structured data generation failed after all retries',
  };
}